<?php
// app/Controllers/MTDReportsController.php

namespace App\Controllers;

use App\Models\MtdpModel;
use App\Models\MtdpSpaModel;
use App\Models\MtdpDipModel;
use App\Models\MtdpKraModel;
use App\Models\MtdpSpecificAreaModel;
use App\Models\MtdpInvestmentsModel;
use App\Models\MtdpStrategiesModel;
use App\Models\MtdpIndicatorsModel;
use App\Models\WorkplanMtdpLinkModel;
use CodeIgniter\Controller;

class MTDReportsController extends Controller
{
    /**
     * Display the MTDP Plans Report (Read-only)
     */
    public function index()
    {
        $mtdpModel = new MtdpModel();
        $spaModel = new MtdpSpaModel();
        $dipModel = new MtdpDipModel();
        $kraModel = new MtdpKraModel();
        $saModel = new MtdpSpecificAreaModel();
        $investmentsModel = new MtdpInvestmentsModel();
        $strategiesModel = new MtdpStrategiesModel();
        $indicatorsModel = new MtdpIndicatorsModel();
        $workplanLinkModel = new WorkplanMtdpLinkModel();

        // Get date filters from request
        $dateFrom = $this->request->getGet('date_from');
        $dateTo = $this->request->getGet('date_to');

        // Get all MTDP data with optional date filtering
        $plans = $mtdpModel->findAll();
        $spas = $this->getFilteredData($spaModel, $dateFrom, $dateTo);
        $dips = $this->getFilteredData($dipModel, $dateFrom, $dateTo);
        $kras = $this->getFilteredData($kraModel, $dateFrom, $dateTo);
        $specific_areas = $this->getFilteredData($saModel, $dateFrom, $dateTo);
        $investments = $this->getFilteredData($investmentsModel, $dateFrom, $dateTo);
        $strategies = $this->getFilteredData($strategiesModel, $dateFrom, $dateTo);
        $indicators = $this->getFilteredData($indicatorsModel, $dateFrom, $dateTo);

        // Get workplan counts for each MTDP element
        $workplanCounts = $this->getWorkplanCounts($workplanLinkModel, $dateFrom, $dateTo);

        // Prepare chart data
        $chartData = $this->prepareChartData($plans, $spas, $dips, $kras, $specific_areas, $investments, $strategies, $indicators, $dateFrom, $dateTo);

        // Pass all data to the view
        return view('reports_mtdp/reports_mtdp_index', [
            'title' => 'MTDP Plans Report',
            'plans' => $plans,
            'spas' => $spas,
            'dips' => $dips,
            'kras' => $kras,
            'specific_areas' => $specific_areas,
            'investments' => $investments,
            'strategies' => $strategies,
            'indicators' => $indicators,
            'chartData' => $chartData,
            'workplanCounts' => $workplanCounts,
            'dateFrom' => $dateFrom,
            'dateTo' => $dateTo,
        ]);
    }

    /**
     * Prepare data for charts and graphs
     */
    private function prepareChartData($plans, $spas, $dips, $kras, $specific_areas, $investments, $strategies, $indicators, $dateFrom = null, $dateTo = null)
    {
        $chartData = [];

        // 1. Investment distribution by year
        $yearlyInvestments = [
            'year_one' => 0,
            'year_two' => 0,
            'year_three' => 0,
            'year_four' => 0,
            'year_five' => 0
        ];

        foreach ($investments as $investment) {
            $yearlyInvestments['year_one'] += (float)($investment['year_one'] ?? 0);
            $yearlyInvestments['year_two'] += (float)($investment['year_two'] ?? 0);
            $yearlyInvestments['year_three'] += (float)($investment['year_three'] ?? 0);
            $yearlyInvestments['year_four'] += (float)($investment['year_four'] ?? 0);
            $yearlyInvestments['year_five'] += (float)($investment['year_five'] ?? 0);
        }

        $chartData['yearlyInvestments'] = $yearlyInvestments;

        // 2. Investment distribution by DIP
        $dipInvestments = [];
        foreach ($dips as $dip) {
            $dipInvestments[$dip['id']] = [
                'title' => $dip['dip_title'],
                'total' => 0
            ];
        }

        foreach ($investments as $investment) {
            if (isset($dipInvestments[$investment['dip_id']])) {
                $total = (float)($investment['year_one'] ?? 0) +
                         (float)($investment['year_two'] ?? 0) +
                         (float)($investment['year_three'] ?? 0) +
                         (float)($investment['year_four'] ?? 0) +
                         (float)($investment['year_five'] ?? 0);
                $dipInvestments[$investment['dip_id']]['total'] += $total;
            }
        }

        $chartData['dipInvestments'] = $dipInvestments;

        // 3. Status distribution
        $statusCounts = [
            'dips' => ['active' => 0, 'inactive' => 0],
            'kras' => ['active' => 0, 'inactive' => 0],
            'specific_areas' => ['active' => 0, 'inactive' => 0],
            'strategies' => ['active' => 0, 'inactive' => 0],
            'indicators' => ['active' => 0, 'inactive' => 0]
        ];

        foreach ($dips as $dip) {
            $statusCounts['dips'][$dip['dip_status'] == 1 ? 'active' : 'inactive']++;
        }

        foreach ($kras as $kra) {
            $statusCounts['kras'][$kra['kra_status'] == 1 ? 'active' : 'inactive']++;
        }

        foreach ($specific_areas as $sa) {
            $statusCounts['specific_areas'][$sa['sa_status'] == 1 ? 'active' : 'inactive']++;
        }

        foreach ($strategies as $strategy) {
            $statusCounts['strategies'][$strategy['strategies_status'] == 1 ? 'active' : 'inactive']++;
        }

        foreach ($indicators as $indicator) {
            $statusCounts['indicators'][$indicator['indicators_status'] == 1 ? 'active' : 'inactive']++;
        }

        $chartData['statusCounts'] = $statusCounts;

        // 4. Count entities by MTDP plan
        $entitiesByPlan = [];
        foreach ($plans as $plan) {
            $entitiesByPlan[$plan['id']] = [
                'title' => $plan['title'],
                'spas' => 0,
                'dips' => 0,
                'kras' => 0,
                'specific_areas' => 0,
                'investments' => 0,
                'strategies' => 0,
                'indicators' => 0
            ];
        }

        foreach ($spas as $spa) {
            if (isset($entitiesByPlan[$spa['mtdp_id']])) {
                $entitiesByPlan[$spa['mtdp_id']]['spas']++;
            }
        }

        foreach ($dips as $dip) {
            if (isset($entitiesByPlan[$dip['mtdp_id']])) {
                $entitiesByPlan[$dip['mtdp_id']]['dips']++;
            }
        }

        foreach ($kras as $kra) {
            if (isset($entitiesByPlan[$kra['mtdp_id']])) {
                $entitiesByPlan[$kra['mtdp_id']]['kras']++;
            }
        }

        foreach ($specific_areas as $sa) {
            if (isset($entitiesByPlan[$sa['mtdp_id']])) {
                $entitiesByPlan[$sa['mtdp_id']]['specific_areas']++;
            }
        }

        foreach ($investments as $investment) {
            if (isset($entitiesByPlan[$investment['mtdp_id']])) {
                $entitiesByPlan[$investment['mtdp_id']]['investments']++;
            }
        }

        foreach ($strategies as $strategy) {
            if (isset($entitiesByPlan[$strategy['mtdp_id']])) {
                $entitiesByPlan[$strategy['mtdp_id']]['strategies']++;
            }
        }

        foreach ($indicators as $indicator) {
            if (isset($entitiesByPlan[$indicator['mtdp_id']])) {
                $entitiesByPlan[$indicator['mtdp_id']]['indicators']++;
            }
        }

        $chartData['entitiesByPlan'] = $entitiesByPlan;

        return $chartData;
    }




    /**
     * Get filtered data based on date range
     */
    private function getFilteredData($model, $dateFrom = null, $dateTo = null)
    {
        if ($dateFrom && $dateTo) {
            return $model->where('created_at >=', $dateFrom)
                        ->where('created_at <=', $dateTo . ' 23:59:59')
                        ->findAll();
        } elseif ($dateFrom) {
            return $model->where('created_at >=', $dateFrom)->findAll();
        } elseif ($dateTo) {
            return $model->where('created_at <=', $dateTo . ' 23:59:59')->findAll();
        }

        return $model->findAll();
    }

    /**
     * Get workplan counts for MTDP elements
     */
    private function getWorkplanCounts($workplanLinkModel, $dateFrom = null, $dateTo = null)
    {
        $counts = [
            'strategies' => [],
            'kras' => [],
            'dips' => [],
            'spas' => []
        ];

        // Build base query with date filtering
        $builder = $workplanLinkModel->builder();
        if ($dateFrom && $dateTo) {
            $builder->where('created_at >=', $dateFrom)
                   ->where('created_at <=', $dateTo . ' 23:59:59');
        } elseif ($dateFrom) {
            $builder->where('created_at >=', $dateFrom);
        } elseif ($dateTo) {
            $builder->where('created_at <=', $dateTo . ' 23:59:59');
        }

        // Count workplans for strategies
        $strategyCounts = $builder->select('strategies_id, COUNT(*) as count')
                                ->where('strategies_id IS NOT NULL')
                                ->groupBy('strategies_id')
                                ->get()->getResultArray();
        foreach ($strategyCounts as $count) {
            $counts['strategies'][$count['strategies_id']] = $count['count'];
        }

        // Reset builder for KRAs
        $builder = $workplanLinkModel->builder();
        if ($dateFrom && $dateTo) {
            $builder->where('created_at >=', $dateFrom)
                   ->where('created_at <=', $dateTo . ' 23:59:59');
        } elseif ($dateFrom) {
            $builder->where('created_at >=', $dateFrom);
        } elseif ($dateTo) {
            $builder->where('created_at <=', $dateTo . ' 23:59:59');
        }

        $kraCounts = $builder->select('kra_id, COUNT(*) as count')
                            ->where('kra_id IS NOT NULL')
                            ->groupBy('kra_id')
                            ->get()->getResultArray();
        foreach ($kraCounts as $count) {
            $counts['kras'][$count['kra_id']] = $count['count'];
        }

        // Reset builder for DIPs
        $builder = $workplanLinkModel->builder();
        if ($dateFrom && $dateTo) {
            $builder->where('created_at >=', $dateFrom)
                   ->where('created_at <=', $dateTo . ' 23:59:59');
        } elseif ($dateFrom) {
            $builder->where('created_at >=', $dateFrom);
        } elseif ($dateTo) {
            $builder->where('created_at <=', $dateTo . ' 23:59:59');
        }

        $dipCounts = $builder->select('dip_id, COUNT(*) as count')
                            ->where('dip_id IS NOT NULL')
                            ->groupBy('dip_id')
                            ->get()->getResultArray();
        foreach ($dipCounts as $count) {
            $counts['dips'][$count['dip_id']] = $count['count'];
        }

        // Reset builder for SPAs
        $builder = $workplanLinkModel->builder();
        if ($dateFrom && $dateTo) {
            $builder->where('created_at >=', $dateFrom)
                   ->where('created_at <=', $dateTo . ' 23:59:59');
        } elseif ($dateFrom) {
            $builder->where('created_at >=', $dateFrom);
        } elseif ($dateTo) {
            $builder->where('created_at <=', $dateTo . ' 23:59:59');
        }

        $spaCounts = $builder->select('spa_id, COUNT(*) as count')
                            ->where('spa_id IS NOT NULL')
                            ->groupBy('spa_id')
                            ->get()->getResultArray();
        foreach ($spaCounts as $count) {
            $counts['spas'][$count['spa_id']] = $count['count'];
        }

        return $counts;
    }
}